#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类目数据补充工具
功能：将"类目补充响应数据.md"文件中存在但"Category data.txt"文件中缺失的类目数据补充到"Category data.txt"中
"""

import json
import os
from typing import Dict, List, Any, Set


class CategorySupplementTool:
    def __init__(self, category_data_file: str = "data/Category data.txt", 
                 supplement_data_file: str = "类目补充响应数据.md"):
        """
        初始化类目补充工具
        
        Args:
            category_data_file: 原始类目数据文件路径
            supplement_data_file: 补充类目数据文件路径
        """
        self.category_data_file = category_data_file
        self.supplement_data_file = supplement_data_file
        self.category_data = {}
        self.supplement_data = {}
        
    def load_category_data(self) -> Dict:
        """加载原始类目数据"""
        try:
            with open(self.category_data_file, 'r', encoding='utf-8') as f:
                self.category_data = json.load(f)
            print(f"✓ 成功加载原始类目数据: {self.category_data_file}")
            return self.category_data
        except Exception as e:
            print(f"✗ 加载原始类目数据失败: {e}")
            return {}
    
    def load_supplement_data(self) -> Dict:
        """加载补充类目数据"""
        try:
            with open(self.supplement_data_file, 'r', encoding='utf-8') as f:
                self.supplement_data = json.load(f)
            print(f"✓ 成功加载补充类目数据: {self.supplement_data_file}")
            return self.supplement_data
        except Exception as e:
            print(f"✗ 加载补充类目数据失败: {e}")
            return {}
    
    def extract_existing_categories(self, data: Dict, level: int = 1) -> Set[str]:
        """
        递归提取现有类目的key集合
        
        Args:
            data: 类目数据
            level: 当前层级
            
        Returns:
            Set[str]: 类目key集合
        """
        categories = set()
        
        if isinstance(data, dict):
            for key, value in data.items():
                if key == "children" and isinstance(value, dict):
                    # 处理children字典
                    for child_key, child_value in value.items():
                        if isinstance(child_value, dict) and "key" in child_value:
                            categories.add(str(child_value["key"]))
                        categories.update(self.extract_existing_categories(child_value, level + 1))
                elif key == "children" and isinstance(value, list):
                    # 处理children列表
                    for item in value:
                        if isinstance(item, dict) and "key" in item:
                            categories.add(str(item["key"]))
                        categories.update(self.extract_existing_categories(item, level + 1))
                elif isinstance(value, dict) and "key" in value:
                    categories.add(str(value["key"]))
                    categories.update(self.extract_existing_categories(value, level + 1))
                elif isinstance(value, (dict, list)):
                    categories.update(self.extract_existing_categories(value, level + 1))
        elif isinstance(data, list):
            for item in data:
                categories.update(self.extract_existing_categories(item, level + 1))
                
        return categories
    
    def extract_supplement_categories(self, category_list: List[Dict]) -> Dict[str, Dict]:
        """
        从补充数据中提取类目信息
        
        Args:
            category_list: 补充数据中的categoryList
            
        Returns:
            Dict[str, Dict]: 按hierarchy分组的类目数据
        """
        categories_by_hierarchy = {
            "1": {},  # 一级类目 -> 对应原数据的二级类目
            "2": {}   # 二级类目 -> 对应原数据的三级类目
        }
        
        def process_category(cat: Dict, parent_id: str = None):
            """递归处理类目"""
            hierarchy = str(cat.get("hierarchy", 0))
            category_id = str(cat.get("categoryId", ""))
            category_name = cat.get("categoryName", "")
            category_pid = str(cat.get("categoryPid", ""))
            
            if hierarchy in ["1", "2"]:
                categories_by_hierarchy[hierarchy][category_id] = {
                    "categoryId": category_id,
                    "categoryName": category_name,
                    "categoryPid": category_pid,
                    "hierarchy": int(hierarchy),
                    "parent_id": parent_id
                }
            
            # 递归处理子类目
            if "childCategory" in cat and isinstance(cat["childCategory"], list):
                for child in cat["childCategory"]:
                    process_category(child, category_id)
        
        # 处理所有类目
        for category in category_list:
            process_category(category)
            
        return categories_by_hierarchy
    
    def find_parent_category(self, parent_id: str, data: Dict) -> Dict:
        """
        在原始数据中查找父类目
        
        Args:
            parent_id: 父类目ID
            data: 原始类目数据
            
        Returns:
            Dict: 找到的父类目数据，如果未找到返回None
        """
        def search_recursive(current_data: Dict, target_id: str) -> Dict:
            if isinstance(current_data, dict):
                # 检查当前节点
                if current_data.get("key") == target_id:
                    return current_data
                
                # 递归搜索children
                if "children" in current_data:
                    if isinstance(current_data["children"], dict):
                        for child_key, child_value in current_data["children"].items():
                            result = search_recursive(child_value, target_id)
                            if result:
                                return result
                    elif isinstance(current_data["children"], list):
                        for child in current_data["children"]:
                            result = search_recursive(child, target_id)
                            if result:
                                return result
                
                # 递归搜索其他字典值
                for key, value in current_data.items():
                    if key != "children" and isinstance(value, dict):
                        result = search_recursive(value, target_id)
                        if result:
                            return result
            
            return None
        
        return search_recursive(data, parent_id)
    
    def add_missing_categories(self) -> bool:
        """
        添加缺失的类目到原始数据中
        
        Returns:
            bool: 是否成功添加
        """
        try:
            # 获取现有类目集合
            existing_categories = self.extract_existing_categories(self.category_data)
            print(f"✓ 现有类目数量: {len(existing_categories)}")
            
            # 提取补充类目
            supplement_categories = self.extract_supplement_categories(
                self.supplement_data.get("categoryList", [])
            )
            
            added_count = 0
            
            # 处理hierarchy=1的类目（对应原数据的二级类目）
            for cat_id, cat_info in supplement_categories["1"].items():
                if cat_id not in existing_categories:
                    parent_id = cat_info["categoryPid"]
                    parent_category = self.find_parent_category(parent_id, self.category_data)
                    
                    if parent_category and "children" in parent_category:
                        # 添加到父类目的children中
                        new_category = {
                            "name": cat_info["categoryName"],
                            "key": cat_id,
                            "categoryPid": parent_id,
                            "children": {}
                        }
                        parent_category["children"][cat_info["categoryName"]] = new_category
                        added_count += 1
                        print(f"✓ 添加二级类目: {cat_info['categoryName']} (ID: {cat_id})")
            
            # 处理hierarchy=2的类目（对应原数据的三级类目）
            for cat_id, cat_info in supplement_categories["2"].items():
                if cat_id not in existing_categories:
                    parent_id = cat_info["categoryPid"]
                    parent_category = self.find_parent_category(parent_id, self.category_data)
                    
                    if parent_category:
                        # 确保children是列表格式
                        if "children" not in parent_category:
                            parent_category["children"] = []
                        elif isinstance(parent_category["children"], dict):
                            # 如果是字典格式，保持不变，这里需要特殊处理
                            continue
                        
                        if isinstance(parent_category["children"], list):
                            new_category = {
                                "name": cat_info["categoryName"],
                                "key": cat_id,
                                "categoryPid": parent_id
                            }
                            parent_category["children"].append(new_category)
                            added_count += 1
                            print(f"✓ 添加三级类目: {cat_info['categoryName']} (ID: {cat_id})")
            
            print(f"✓ 总共添加了 {added_count} 个类目")
            return True
            
        except Exception as e:
            print(f"✗ 添加类目失败: {e}")
            return False
    
    def save_updated_data(self, backup: bool = True) -> bool:
        """
        保存更新后的数据
        
        Args:
            backup: 是否创建备份文件
            
        Returns:
            bool: 是否成功保存
        """
        try:
            # 创建备份
            if backup:
                backup_file = self.category_data_file + ".backup"
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(self.category_data, f, ensure_ascii=False, indent=2)
                print(f"✓ 创建备份文件: {backup_file}")
            
            # 保存更新后的数据
            with open(self.category_data_file, 'w', encoding='utf-8') as f:
                json.dump(self.category_data, f, ensure_ascii=False, indent=2)
            print(f"✓ 保存更新后的数据: {self.category_data_file}")
            
            return True
            
        except Exception as e:
            print(f"✗ 保存数据失败: {e}")
            return False
    
    def validate_json_format(self) -> bool:
        """验证JSON格式的有效性"""
        try:
            # 重新加载验证
            with open(self.category_data_file, 'r', encoding='utf-8') as f:
                json.load(f)
            print("✓ JSON格式验证通过")
            return True
        except Exception as e:
            print(f"✗ JSON格式验证失败: {e}")
            return False
    
    def run(self) -> bool:
        """
        执行完整的类目补充流程
        
        Returns:
            bool: 是否成功完成
        """
        print("=" * 50)
        print("开始执行类目数据补充...")
        print("=" * 50)
        
        # 1. 加载数据
        if not self.load_category_data() or not self.load_supplement_data():
            return False
        
        # 2. 添加缺失类目
        if not self.add_missing_categories():
            return False
        
        # 3. 保存更新后的数据
        if not self.save_updated_data():
            return False
        
        # 4. 验证JSON格式
        if not self.validate_json_format():
            return False
        
        print("=" * 50)
        print("类目数据补充完成！")
        print("=" * 50)
        return True


def main():
    """主函数"""
    tool = CategorySupplementTool()
    success = tool.run()
    
    if success:
        print("\n🎉 类目补充成功完成！")
    else:
        print("\n❌ 类目补充过程中出现错误，请检查日志信息。")


if __name__ == "__main__":
    main()
