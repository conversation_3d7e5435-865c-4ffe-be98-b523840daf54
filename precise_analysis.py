#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的类目数据分析和补充工具
"""

import json
import re
from typing import Set, Dict, List


def extract_all_keys_from_original(file_path: str) -> Set[str]:
    """从原始文件中提取所有的key值"""
    keys = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 使用正则表达式查找所有的key值
            key_pattern = r'"key":\s*"([^"]+)"'
            matches = re.findall(key_pattern, content)
            keys.update(matches)
        print(f"从原始文件中提取到 {len(keys)} 个key")
        return keys
    except Exception as e:
        print(f"提取原始文件key失败: {e}")
        return set()


def extract_categories_from_supplement(file_path: str) -> List[Dict]:
    """从补充文件中提取所有类目"""
    categories = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        def extract_recursive(cat_list):
            for cat in cat_list:
                categories.append({
                    'categoryId': str(cat.get('categoryId', '')),
                    'categoryName': cat.get('categoryName', ''),
                    'categoryPid': str(cat.get('categoryPid', '')),
                    'hierarchy': cat.get('hierarchy', 0)
                })
                
                if 'childCategory' in cat and cat['childCategory']:
                    extract_recursive(cat['childCategory'])
        
        category_list = data.get('categoryList', [])
        extract_recursive(category_list)
        
        print(f"从补充文件中提取到 {len(categories)} 个类目")
        return categories
    except Exception as e:
        print(f"提取补充文件类目失败: {e}")
        return []


def find_missing_categories(original_keys: Set[str], supplement_categories: List[Dict]) -> List[Dict]:
    """找出缺失的类目"""
    missing = []
    for cat in supplement_categories:
        if cat['categoryId'] not in original_keys:
            missing.append(cat)
    return missing


def add_categories_to_original(original_file: str, missing_categories: List[Dict]) -> bool:
    """将缺失的类目添加到原始文件中"""
    try:
        # 加载原始数据
        with open(original_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 创建备份
        backup_file = original_file + '.backup'
        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"创建备份文件: {backup_file}")
        
        added_count = 0
        
        # 按hierarchy分组处理
        hierarchy_1_cats = [cat for cat in missing_categories if cat['hierarchy'] == 1]
        hierarchy_2_cats = [cat for cat in missing_categories if cat['hierarchy'] == 2]
        
        print(f"需要添加的hierarchy=1类目: {len(hierarchy_1_cats)}")
        print(f"需要添加的hierarchy=2类目: {len(hierarchy_2_cats)}")
        
        # 处理hierarchy=1的类目（对应原数据的二级类目）
        for cat in hierarchy_1_cats:
            parent_id = cat['categoryPid']
            # 在原数据中查找父类目
            parent_found = find_and_add_to_parent(data, parent_id, cat, 'dict')
            if parent_found:
                added_count += 1
                print(f"✓ 添加二级类目: {cat['categoryName']} (ID: {cat['categoryId']})")
        
        # 处理hierarchy=2的类目（对应原数据的三级类目）
        for cat in hierarchy_2_cats:
            parent_id = cat['categoryPid']
            # 在原数据中查找父类目
            parent_found = find_and_add_to_parent(data, parent_id, cat, 'list')
            if parent_found:
                added_count += 1
                print(f"✓ 添加三级类目: {cat['categoryName']} (ID: {cat['categoryId']})")
        
        # 保存更新后的数据
        with open(original_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"总共添加了 {added_count} 个类目")
        return True
        
    except Exception as e:
        print(f"添加类目失败: {e}")
        return False


def find_and_add_to_parent(data: Dict, parent_id: str, new_cat: Dict, children_type: str) -> bool:
    """在数据中查找父类目并添加新类目"""
    def search_recursive(current_data):
        if isinstance(current_data, dict):
            # 检查当前节点是否是目标父类目
            if current_data.get('key') == parent_id:
                # 找到父类目，添加新类目
                if children_type == 'dict':
                    # 添加到children字典中
                    if 'children' not in current_data:
                        current_data['children'] = {}
                    current_data['children'][new_cat['categoryName']] = {
                        'name': new_cat['categoryName'],
                        'key': new_cat['categoryId'],
                        'categoryPid': parent_id,
                        'children': {}
                    }
                else:
                    # 添加到children列表中
                    if 'children' not in current_data:
                        current_data['children'] = []
                    elif isinstance(current_data['children'], dict):
                        # 如果当前是字典，转换为列表
                        current_data['children'] = []
                    
                    current_data['children'].append({
                        'name': new_cat['categoryName'],
                        'key': new_cat['categoryId'],
                        'categoryPid': parent_id
                    })
                return True
            
            # 递归搜索
            for key, value in current_data.items():
                if isinstance(value, (dict, list)):
                    if search_recursive(value):
                        return True
        elif isinstance(current_data, list):
            for item in current_data:
                if search_recursive(item):
                    return True
        return False
    
    return search_recursive(data)


def main():
    print("=" * 60)
    print("精确类目数据分析和补充")
    print("=" * 60)
    
    original_file = "data/Category data.txt"
    supplement_file = "类目补充响应数据.md"
    
    # 1. 提取原始数据中的所有key
    original_keys = extract_all_keys_from_original(original_file)
    
    # 2. 提取补充数据中的所有类目
    supplement_categories = extract_categories_from_supplement(supplement_file)
    
    # 3. 找出缺失的类目
    missing_categories = find_missing_categories(original_keys, supplement_categories)
    
    print(f"\n发现 {len(missing_categories)} 个缺失的类目:")
    
    # 按hierarchy分组显示
    hierarchy_groups = {}
    for cat in missing_categories:
        h = cat['hierarchy']
        if h not in hierarchy_groups:
            hierarchy_groups[h] = []
        hierarchy_groups[h].append(cat)
    
    for hierarchy, cats in sorted(hierarchy_groups.items()):
        print(f"\nHierarchy {hierarchy} ({len(cats)} 个):")
        for cat in cats[:5]:  # 只显示前5个
            print(f"  - {cat['categoryName']} (ID: {cat['categoryId']}, PID: {cat['categoryPid']})")
        if len(cats) > 5:
            print(f"  ... 还有 {len(cats) - 5} 个")
    
    # 4. 如果有缺失的类目，询问是否添加
    if missing_categories:
        response = input(f"\n是否将这 {len(missing_categories)} 个缺失的类目添加到原始文件中？(y/n): ")
        if response.lower() == 'y':
            success = add_categories_to_original(original_file, missing_categories)
            if success:
                print("✓ 类目补充完成！")
            else:
                print("✗ 类目补充失败！")
        else:
            print("取消添加操作")
    else:
        print("\n✓ 没有发现缺失的类目，原始数据已经包含了补充数据中的所有类目。")


if __name__ == "__main__":
    main()
