2025-08-03 05:08:39,667 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 05:08:39,668 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 05:08:39,683 - INFO - 类目数据加载成功
2025-08-03 05:08:39,684 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 05:08:39,687 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,318 - INFO - 成功获取 100 条基础数据
2025-08-03 05:08:41,319 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-03 05:08:41,323 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,324 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,324 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,394 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,412 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,435 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,410 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,462 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,483 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,434 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,504 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,532 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,464 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,549 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,588 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,581 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,581 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,598 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:47,565 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:47,588 - INFO - 成功加载 9 个Cookie
2025-08-03 05:09:31,044 - INFO - DataCollector资源清理完成
2025-08-03 05:15:28,040 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '抑菌/消毒乳膏', '售卖渠道': '商品卡', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 05:15:28,041 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 05:15:28,054 - INFO - 类目数据加载成功
2025-08-03 05:15:28,055 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "categoryLevel2Id", "value": ["1662"]}, {"code": "categoryLevel3Id", "value": ["29366"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 05:15:28,057 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,903 - INFO - 成功获取 100 条基础数据
2025-08-03 05:15:28,904 - INFO - 遇到成交指数 98 小于最小值 100，停止采集
2025-08-03 05:15:28,904 - INFO - 成交指数过滤：原始 100 条，过滤后 68 条
2025-08-03 05:15:28,908 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,890 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,933 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,917 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,994 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,869 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,904 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,937 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,845 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,932 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,820 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,885 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,921 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,849 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,863 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,892 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,839 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,952 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,953 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:36,957 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,012 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,012 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,899 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,974 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,993 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,867 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,982 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:39,885 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:39,891 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:40,022 - INFO - 成功加载 9 个Cookie
2025-08-03 06:33:07,997 - INFO - DataCollector资源清理完成
