2025-08-03 05:08:39,667 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '', '二级类目': '', '三级类目': '', '四级类目': '', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 05:08:39,668 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 05:08:39,683 - INFO - 类目数据加载成功
2025-08-03 05:08:39,684 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 05:08:39,687 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,318 - INFO - 成功获取 100 条基础数据
2025-08-03 05:08:41,319 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-03 05:08:41,323 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,324 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:41,324 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,394 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,412 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:42,435 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,410 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,462 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:43,483 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,434 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,504 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:44,532 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,464 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,549 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:45,588 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,581 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,581 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:46,598 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:47,565 - INFO - 成功加载 9 个Cookie
2025-08-03 05:08:47,588 - INFO - 成功加载 9 个Cookie
2025-08-03 05:09:31,044 - INFO - DataCollector资源清理完成
2025-08-03 05:15:28,040 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '个护清洁', '三级类目': '身体护理', '四级类目': '抑菌/消毒乳膏', '售卖渠道': '商品卡', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 05:15:28,041 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 05:15:28,054 - INFO - 类目数据加载成功
2025-08-03 05:15:28,055 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1060"]}, {"code": "categoryLevel2Id", "value": ["1662"]}, {"code": "categoryLevel3Id", "value": ["29366"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 05:15:28,057 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,903 - INFO - 成功获取 100 条基础数据
2025-08-03 05:15:28,904 - INFO - 遇到成交指数 98 小于最小值 100，停止采集
2025-08-03 05:15:28,904 - INFO - 成交指数过滤：原始 100 条，过滤后 68 条
2025-08-03 05:15:28,908 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:28,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,890 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,909 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:29,933 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,917 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:30,994 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,869 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,904 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:31,937 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,845 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:32,932 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,820 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,885 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:33,921 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,849 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,863 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:34,892 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,839 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,952 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:35,953 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:36,957 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,012 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,012 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,899 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,974 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:37,993 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,867 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,903 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:38,982 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:39,885 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:39,891 - INFO - 成功加载 9 个Cookie
2025-08-03 05:15:40,022 - INFO - 成功加载 9 个Cookie
2025-08-03 06:33:07,997 - INFO - DataCollector资源清理完成
2025-08-03 06:57:42,121 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '家居生活', '三级类目': '居家清洁', '四级类目': '纸品湿巾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 06:57:42,122 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 06:57:42,160 - INFO - 类目数据加载成功
2025-08-03 06:57:42,161 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1107"]}, {"code": "categoryLevel2Id", "value": ["1354"]}, {"code": "categoryLevel3Id", "value": ["3037"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 06:57:42,163 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:43,107 - INFO - 成功获取 100 条基础数据
2025-08-03 06:57:43,108 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-03 06:57:43,112 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:43,112 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:43,113 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:44,116 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:44,117 - INFO - 成功加载 9 个Cookie
2025-08-03 06:57:44,124 - INFO - 成功加载 9 个Cookie
2025-08-03 07:04:11,178 - INFO - DataCollector资源清理完成
2025-08-03 07:05:11,351 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '家居生活', '三级类目': '居家清洁', '四级类目': '纸品湿巾', '售卖渠道': '全部', '售卖形式': '全部', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 07:05:11,352 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 07:05:11,390 - INFO - 类目数据加载成功
2025-08-03 07:05:11,392 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1107"]}, {"code": "categoryLevel2Id", "value": ["1354"]}, {"code": "categoryLevel3Id", "value": ["3037"]}, {"code": "saleChannel", "value": ["all"]}, {"code": "saleType", "value": ["all"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 07:05:11,394 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:12,048 - INFO - 成功获取 100 条基础数据
2025-08-03 07:05:12,049 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-03 07:05:12,053 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:12,054 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:12,055 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:18,605 - INFO - 设置筛选条件: {'日期': '本周 (2025-07-28-2025-08-03)', '一级类目': '个护日百行业', '二级类目': '家居生活', '三级类目': '居家清洁', '四级类目': '纸品湿巾', '售卖渠道': '商品卡', '售卖形式': '自卖', '品牌商品': '全部', '大牌大补': '全部'}
2025-08-03 07:05:18,609 - INFO - 设置过滤设置: {'成交指数最小值': 100, '成交指数最大值': 999999, '渠道占比最小值': 85.0, '渠道占比最大值': 105.0}
2025-08-03 07:05:18,661 - INFO - 类目数据加载成功
2025-08-03 07:05:18,662 - INFO - 生成请求载荷: {"module": "sytWebItemTopRank", "timeRange": "CUSTOMIZED_WEEK", "currentStartDay": "2025-07-28", "currentEndDay": "2025-08-03", "compareStartDay": "2025-07-21", "compareEndDay": "2025-07-27", "param": [{"code": "industryId", "value": ["个护日百行业"]}, {"code": "categoryLevel1Id", "value": ["1107"]}, {"code": "categoryLevel2Id", "value": ["1354"]}, {"code": "categoryLevel3Id", "value": ["3037"]}, {"code": "saleChannel", "value": ["商品卡"]}, {"code": "saleType", "value": ["否"]}, {"code": "brandItem", "value": ["all"]}, {"code": "subsidyItem", "value": ["all"]}], "pageNum": 1, "pageSize": 100}
2025-08-03 07:05:18,665 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:19,573 - INFO - 成功获取 100 条基础数据
2025-08-03 07:05:19,574 - INFO - 成交指数过滤：原始 100 条，过滤后 100 条
2025-08-03 07:05:19,576 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:19,578 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:19,581 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:20,574 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:20,588 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:20,589 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:21,547 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:21,547 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:21,571 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:22,497 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:22,507 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:22,507 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:23,450 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:23,464 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:23,499 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:24,420 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:24,449 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:24,482 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:25,403 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:25,413 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:25,462 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:26,379 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:26,396 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:26,448 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:27,348 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:27,368 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:27,446 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:28,355 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:28,367 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:28,398 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:29,313 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:29,324 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:29,337 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:30,294 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:30,303 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:30,310 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:31,245 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:31,268 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:31,282 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:32,213 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:32,243 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:32,266 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:33,071 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:33,204 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:33,266 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:33,970 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:34,126 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:34,239 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:34,879 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:35,029 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:35,149 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:35,768 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:35,938 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:36,038 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:36,632 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:36,823 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:36,896 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:37,726 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:37,769 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:37,852 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:38,690 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:38,697 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:38,825 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:39,675 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:39,696 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:39,800 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:40,650 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:40,659 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:40,747 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:41,622 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:41,631 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:41,749 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:42,573 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:42,624 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:42,674 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:43,568 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:43,568 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:43,647 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:44,480 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:44,508 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:44,788 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:45,369 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:45,412 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:45,627 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:46,329 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:46,332 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:46,454 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:47,237 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:47,316 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:47,372 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:48,231 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:48,246 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:48,399 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:49,254 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:49,254 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:49,337 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:50,232 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:50,240 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:50,296 - INFO - 成功加载 9 个Cookie
2025-08-03 07:05:51,198 - INFO - 成功加载 9 个Cookie
2025-08-03 07:06:32,695 - INFO - DataCollector资源清理完成
