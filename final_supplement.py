#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终的类目补充工具
"""

import json
import re
import os


def main():
    print("开始类目数据补充...")
    
    # 1. 从原始文件中提取所有key
    print("1. 提取原始数据中的key...")
    original_keys = set()
    try:
        with open("data/Category data.txt", 'r', encoding='utf-8') as f:
            content = f.read()
            # 使用正则表达式查找所有key值
            key_pattern = r'"key":\s*"([^"]+)"'
            matches = re.findall(key_pattern, content)
            original_keys.update(matches)
        print(f"   找到 {len(original_keys)} 个现有key")
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    # 2. 加载补充数据
    print("2. 加载补充数据...")
    try:
        with open("类目补充响应数据.md", 'r', encoding='utf-8') as f:
            supplement_data = json.load(f)
        print(f"   补充数据加载成功")
    except Exception as e:
        print(f"   错误: {e}")
        return
    
    # 3. 提取补充数据中的类目
    print("3. 分析补充数据...")
    missing_categories = []
    
    def extract_categories(cat_list, level=1):
        for cat in cat_list:
            cat_id = str(cat.get('categoryId', ''))
            cat_name = cat.get('categoryName', '')
            cat_pid = str(cat.get('categoryPid', ''))
            hierarchy = cat.get('hierarchy', 0)
            
            # 检查是否缺失
            if cat_id and cat_id not in original_keys:
                missing_categories.append({
                    'id': cat_id,
                    'name': cat_name,
                    'pid': cat_pid,
                    'hierarchy': hierarchy
                })
            
            # 递归处理子类目
            if 'childCategory' in cat and cat['childCategory']:
                extract_categories(cat['childCategory'], level + 1)
    
    category_list = supplement_data.get('categoryList', [])
    extract_categories(category_list)
    
    print(f"   发现 {len(missing_categories)} 个缺失的类目")
    
    # 4. 显示缺失的类目
    if missing_categories:
        print("\n缺失的类目:")
        hierarchy_count = {}
        for cat in missing_categories:
            h = cat['hierarchy']
            if h not in hierarchy_count:
                hierarchy_count[h] = 0
            hierarchy_count[h] += 1
        
        for h, count in sorted(hierarchy_count.items()):
            print(f"   Hierarchy {h}: {count} 个")
        
        # 显示前10个示例
        print("\n前10个缺失类目示例:")
        for i, cat in enumerate(missing_categories[:10]):
            print(f"   {i+1}. {cat['name']} (ID: {cat['id']}, PID: {cat['pid']}, H: {cat['hierarchy']})")
        
        if len(missing_categories) > 10:
            print(f"   ... 还有 {len(missing_categories) - 10} 个")
    
    # 5. 如果有缺失类目，进行补充
    if missing_categories:
        print(f"\n5. 开始补充 {len(missing_categories)} 个缺失类目...")
        
        # 加载原始数据
        try:
            with open("data/Category data.txt", 'r', encoding='utf-8') as f:
                original_data = json.load(f)
        except Exception as e:
            print(f"   加载原始数据失败: {e}")
            return
        
        # 创建备份
        backup_file = "data/Category data.txt.backup"
        try:
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(original_data, f, ensure_ascii=False, indent=2)
            print(f"   创建备份: {backup_file}")
        except Exception as e:
            print(f"   创建备份失败: {e}")
            return
        
        # 添加缺失的类目
        added_count = 0
        
        # 按hierarchy分组处理
        h1_cats = [cat for cat in missing_categories if cat['hierarchy'] == 1]
        h2_cats = [cat for cat in missing_categories if cat['hierarchy'] == 2]
        
        print(f"   处理 {len(h1_cats)} 个hierarchy=1类目...")
        print(f"   处理 {len(h2_cats)} 个hierarchy=2类目...")
        
        # 简单的添加策略：将所有缺失类目添加到一个新的顶级分类中
        if missing_categories:
            # 创建一个新的顶级分类来存放补充的类目
            supplement_category_name = "补充类目"
            if supplement_category_name not in original_data:
                original_data[supplement_category_name] = {
                    "name": supplement_category_name,
                    "key": "supplement_category",
                    "categoryPid": "",
                    "children": {}
                }
            
            # 按hierarchy分组添加
            for hierarchy in [1, 2]:
                cats_for_hierarchy = [cat for cat in missing_categories if cat['hierarchy'] == hierarchy]
                if cats_for_hierarchy:
                    hierarchy_group_name = f"Hierarchy_{hierarchy}_补充类目"
                    original_data[supplement_category_name]["children"][hierarchy_group_name] = {
                        "name": hierarchy_group_name,
                        "key": f"hierarchy_{hierarchy}_supplement",
                        "categoryPid": "supplement_category",
                        "children": []
                    }
                    
                    for cat in cats_for_hierarchy:
                        original_data[supplement_category_name]["children"][hierarchy_group_name]["children"].append({
                            "name": cat['name'],
                            "key": cat['id'],
                            "categoryPid": f"hierarchy_{hierarchy}_supplement"
                        })
                        added_count += 1
        
        # 保存更新后的数据
        try:
            with open("data/Category data.txt", 'w', encoding='utf-8') as f:
                json.dump(original_data, f, ensure_ascii=False, indent=2)
            print(f"   成功添加 {added_count} 个类目")
            print(f"   数据已保存到: data/Category data.txt")
        except Exception as e:
            print(f"   保存失败: {e}")
            return
        
        # 验证JSON格式
        try:
            with open("data/Category data.txt", 'r', encoding='utf-8') as f:
                json.load(f)
            print("   JSON格式验证通过")
        except Exception as e:
            print(f"   JSON格式验证失败: {e}")
            return
        
        print("\n✅ 类目补充完成！")
        print(f"   - 原始数据备份: {backup_file}")
        print(f"   - 更新后数据: data/Category data.txt")
        print(f"   - 新增类目数量: {added_count}")
        
    else:
        print("\n✅ 没有发现缺失的类目，数据已经是最新的！")


if __name__ == "__main__":
    main()
