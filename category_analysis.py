#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
类目数据分析工具
用于分析两个文件中的类目差异
"""

import json
from typing import Dict, List, Set


def load_json_file(file_path: str) -> Dict:
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载文件失败 {file_path}: {e}")
        return {}


def extract_category_keys_from_original(data: Dict, level: int = 1) -> Set[str]:
    """从原始数据中提取所有类目key"""
    keys = set()
    
    def recursive_extract(current_data, current_level):
        if isinstance(current_data, dict):
            # 如果有key字段，添加到集合中
            if "key" in current_data:
                keys.add(str(current_data["key"]))
            
            # 递归处理children
            if "children" in current_data:
                if isinstance(current_data["children"], dict):
                    for child_key, child_value in current_data["children"].items():
                        recursive_extract(child_value, current_level + 1)
                elif isinstance(current_data["children"], list):
                    for child in current_data["children"]:
                        recursive_extract(child, current_level + 1)
            
            # 递归处理其他字典值
            for key, value in current_data.items():
                if key not in ["key", "name", "categoryPid"] and isinstance(value, (dict, list)):
                    recursive_extract(value, current_level + 1)
        elif isinstance(current_data, list):
            for item in current_data:
                recursive_extract(item, current_level + 1)
    
    recursive_extract(data, level)
    return keys


def extract_category_keys_from_supplement(data: Dict) -> Dict[str, List[Dict]]:
    """从补充数据中提取类目信息"""
    categories_by_hierarchy = {"1": [], "2": []}
    
    def process_category(cat: Dict):
        hierarchy = str(cat.get("hierarchy", 0))
        category_id = str(cat.get("categoryId", ""))
        category_name = cat.get("categoryName", "")
        category_pid = str(cat.get("categoryPid", ""))
        
        if hierarchy in ["1", "2"]:
            categories_by_hierarchy[hierarchy].append({
                "categoryId": category_id,
                "categoryName": category_name,
                "categoryPid": category_pid,
                "hierarchy": int(hierarchy)
            })
        
        # 递归处理子类目
        if "childCategory" in cat and isinstance(cat["childCategory"], list):
            for child in cat["childCategory"]:
                process_category(child)
    
    category_list = data.get("categoryList", [])
    for category in category_list:
        process_category(category)
    
    return categories_by_hierarchy


def main():
    """主函数"""
    print("=" * 60)
    print("类目数据分析")
    print("=" * 60)
    
    # 加载数据
    original_data = load_json_file("data/Category data.txt")
    supplement_data = load_json_file("类目补充响应数据.md")
    
    if not original_data or not supplement_data:
        print("数据加载失败")
        return
    
    # 提取原始数据中的类目key
    original_keys = extract_category_keys_from_original(original_data)
    print(f"原始数据中的类目数量: {len(original_keys)}")
    
    # 提取补充数据中的类目
    supplement_categories = extract_category_keys_from_supplement(supplement_data)
    
    print(f"补充数据中hierarchy=1的类目数量: {len(supplement_categories['1'])}")
    print(f"补充数据中hierarchy=2的类目数量: {len(supplement_categories['2'])}")
    
    # 分析差异
    print("\n" + "=" * 60)
    print("差异分析")
    print("=" * 60)
    
    # 检查hierarchy=1的类目
    print("\nHierarchy=1 (对应原数据二级类目) 的缺失类目:")
    missing_h1 = []
    for cat in supplement_categories["1"]:
        if cat["categoryId"] not in original_keys:
            missing_h1.append(cat)
            print(f"  缺失: {cat['categoryName']} (ID: {cat['categoryId']}, PID: {cat['categoryPid']})")
    
    if not missing_h1:
        print("  无缺失类目")
    
    # 检查hierarchy=2的类目
    print("\nHierarchy=2 (对应原数据三级类目) 的缺失类目:")
    missing_h2 = []
    for cat in supplement_categories["2"]:
        if cat["categoryId"] not in original_keys:
            missing_h2.append(cat)
            print(f"  缺失: {cat['categoryName']} (ID: {cat['categoryId']}, PID: {cat['categoryPid']})")
    
    if not missing_h2:
        print("  无缺失类目")
    
    # 显示一些示例数据
    print("\n" + "=" * 60)
    print("示例数据")
    print("=" * 60)
    
    print("\n原始数据中的一些类目key示例:")
    sample_keys = list(original_keys)[:10]
    for key in sample_keys:
        print(f"  {key}")
    
    print("\n补充数据中hierarchy=1的类目示例:")
    for i, cat in enumerate(supplement_categories["1"][:5]):
        print(f"  {cat['categoryName']} (ID: {cat['categoryId']}, PID: {cat['categoryPid']})")
    
    print("\n补充数据中hierarchy=2的类目示例:")
    for i, cat in enumerate(supplement_categories["2"][:5]):
        print(f"  {cat['categoryName']} (ID: {cat['categoryId']}, PID: {cat['categoryPid']})")
    
    # 检查父类目是否存在
    print("\n" + "=" * 60)
    print("父类目存在性检查")
    print("=" * 60)
    
    print("\n检查hierarchy=1类目的父类目:")
    for cat in supplement_categories["1"][:3]:
        parent_exists = cat["categoryPid"] in original_keys
        print(f"  {cat['categoryName']} -> 父类目 {cat['categoryPid']} {'存在' if parent_exists else '不存在'}")
    
    print("\n检查hierarchy=2类目的父类目:")
    for cat in supplement_categories["2"][:3]:
        parent_exists = cat["categoryPid"] in original_keys
        print(f"  {cat['categoryName']} -> 父类目 {cat['categoryPid']} {'存在' if parent_exists else '不存在'}")


if __name__ == "__main__":
    main()
