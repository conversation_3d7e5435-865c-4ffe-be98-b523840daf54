#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证类目补充结果
"""

import json
import re

def main():
    print("=" * 60)
    print("类目补充结果验证")
    print("=" * 60)
    
    # 1. 统计原始文件行数
    try:
        with open("data/Category data.txt.backup", 'r', encoding='utf-8') as f:
            original_lines = len(f.readlines())
        print(f"原始文件行数: {original_lines:,}")
    except:
        print("无法读取备份文件")
        return
    
    # 2. 统计补充后文件行数
    try:
        with open("data/Category data.txt", 'r', encoding='utf-8') as f:
            updated_lines = len(f.readlines())
        print(f"补充后文件行数: {updated_lines:,}")
        print(f"新增行数: {updated_lines - original_lines:,}")
    except:
        print("无法读取更新后文件")
        return
    
    # 3. 验证JSON格式
    try:
        with open("data/Category data.txt", 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("✓ JSON格式验证通过")
    except Exception as e:
        print(f"✗ JSON格式验证失败: {e}")
        return
    
    # 4. 统计类目数量
    def count_categories(data, level=0):
        count = 0
        if isinstance(data, dict):
            if 'key' in data:
                count += 1
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    count += count_categories(value, level + 1)
        elif isinstance(data, list):
            for item in data:
                count += count_categories(item, level + 1)
        return count
    
    total_categories = count_categories(data)
    print(f"总类目数量: {total_categories:,}")
    
    # 5. 检查一些补充的类目示例
    print("\n验证补充的类目示例:")
    
    # 检查功能钟表相关类目
    content = open("data/Category data.txt", 'r', encoding='utf-8').read()
    
    test_categories = [
        ("功能钟表", "29043"),
        ("挂钟", "30758"),
        ("台钟/闹钟", "30761"),
        ("生活服务", "1277"),
        ("物流服务", "4134")
    ]
    
    for name, cat_id in test_categories:
        if f'"key": "{cat_id}"' in content and f'"name": "{name}"' in content:
            print(f"  ✓ {name} (ID: {cat_id}) - 已正确添加")
        else:
            print(f"  ✗ {name} (ID: {cat_id}) - 未找到")
    
    print("\n=" * 60)
    print("验证完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
