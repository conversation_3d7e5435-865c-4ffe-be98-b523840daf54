# 商家ID字段增加说明

## 修改概述
根据需求，在数据展示区域增加了商家ID字段的采集和展示功能，字段映射为`sellerId`。

## 修改内容

### 1. 表头调整
将原来的9个字段扩展为10个字段，新的表头顺序为：
1. 排名
2. 标题  
3. 链接
4. 成交指数
5. 总成交指数
6. 渠道占比
7. **商家ID** (新增)
8. 支付件数
9. 成交商家数
10. 类目

### 2. 数据映射
在`data_collector.py`的`process_basic_item_data`方法中增加了商家ID字段的映射：
```python
"商家ID": str(item.get("sellerId", "")),  # 新增商家ID字段
```

### 3. 列宽设置
为商家ID列设置了专门的列宽：
- 商家ID列：120px (固定宽度)
- 同时调整了类目列的索引（从第9列变为第10列）

## 修改的文件

### 主要文件
1. **main.py**
   - 更新了`create_data_table()`方法中的表头设置
   - 更新了`update_data_table()`方法中的列顺序
   - 更新了`apply_column_widths()`方法中的列宽设置
   - 更新了`on_single_item_ready()`方法中的列顺序

2. **data_collector.py**
   - 在`process_basic_item_data()`方法中增加了sellerId到商家ID的字段映射

### 备份文件
3. **main_backup.py**
   - 同步更新了与main.py相同的修改内容

## 数据源
根据"响应数据格式.md"，商家ID对应API响应中的`sellerId`字段：
```json
{
    "sellerId": 4104465454,
    // 其他字段...
}
```

## 验证结果
通过验证脚本确认：
- ✅ 表头正确包含10个字段，商家ID位于第7列
- ✅ 数据映射正确，sellerId字段成功映射为商家ID
- ✅ 列宽设置正确，商家ID列设置为120px固定宽度
- ✅ 所有相关文件都已同步更新

## 使用说明
1. 启动程序后，数据展示区域的表格将显示包含商家ID的10列数据
2. 商家ID列位于渠道占比和支付件数之间
3. 商家ID列宽度固定为120px，足够显示完整的商家ID数字
4. 在数据采集过程中，商家ID会自动从API响应的sellerId字段中提取并显示

## 注意事项
- 商家ID字段为字符串类型，确保数字ID能正确显示
- 如果API响应中没有sellerId字段，商家ID列将显示为空
- 列宽设置已优化，确保所有字段都能正常显示
