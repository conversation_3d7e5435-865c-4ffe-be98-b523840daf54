#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的类目数据分析
"""

import json

def main():
    print("开始分析...")
    
    # 加载补充数据
    try:
        with open("类目补充响应数据.md", 'r', encoding='utf-8') as f:
            supplement_data = json.load(f)
        print("✓ 补充数据加载成功")
    except Exception as e:
        print(f"✗ 补充数据加载失败: {e}")
        return
    
    # 分析补充数据结构
    category_list = supplement_data.get("categoryList", [])
    print(f"补充数据中有 {len(category_list)} 个一级类目")
    
    # 统计各层级类目数量
    hierarchy_count = {}
    total_categories = 0
    
    def count_categories(categories, level=1):
        global total_categories
        for cat in categories:
            hierarchy = cat.get("hierarchy", 0)
            if hierarchy not in hierarchy_count:
                hierarchy_count[hierarchy] = 0
            hierarchy_count[hierarchy] += 1
            total_categories += 1
            
            # 递归处理子类目
            if "childCategory" in cat and cat["childCategory"]:
                count_categories(cat["childCategory"], level + 1)
    
    count_categories(category_list)
    
    print(f"总类目数量: {total_categories}")
    for h, count in sorted(hierarchy_count.items()):
        print(f"Hierarchy {h}: {count} 个类目")
    
    # 显示一些示例
    print("\n示例类目:")
    for i, cat in enumerate(category_list[:2]):
        print(f"类目 {i+1}: {cat.get('categoryName', 'N/A')} (ID: {cat.get('categoryId', 'N/A')}, Hierarchy: {cat.get('hierarchy', 'N/A')})")
        
        # 显示子类目
        if "childCategory" in cat and cat["childCategory"]:
            for j, child in enumerate(cat["childCategory"][:2]):
                print(f"  子类目 {j+1}: {child.get('categoryName', 'N/A')} (ID: {child.get('categoryId', 'N/A')}, Hierarchy: {child.get('hierarchy', 'N/A')})")
                
                # 显示孙类目
                if "childCategory" in child and child["childCategory"]:
                    for k, grandchild in enumerate(child["childCategory"][:2]):
                        print(f"    孙类目 {k+1}: {grandchild.get('categoryName', 'N/A')} (ID: {grandchild.get('categoryId', 'N/A')}, Hierarchy: {grandchild.get('hierarchy', 'N/A')})")

if __name__ == "__main__":
    main()
